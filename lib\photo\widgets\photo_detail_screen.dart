import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/common/utils/snackbar_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/moderation/utils/featured_assignment_util.dart';
import 'package:portraitmode/moderation/utils/potd_assignment_util.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/utils/archive_assignment_util.dart';
import 'package:portraitmode/photo/utils/comment_util.dart';
import 'package:portraitmode/photo/utils/photo_deletion_util.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/other_photo_bottom_sheet.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/own_photo_bottom_sheet.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_author.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_categories.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_description.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_frame.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_metadata.dart';

class PhotoDetailScreen extends ConsumerStatefulWidget {
  final PhotoData photo;
  final String originScreenName;
  final bool isPhotoDetail;

  const PhotoDetailScreen({
    super.key,
    required this.photo,
    required this.originScreenName,
    this.isPhotoDetail = true,
  });

  @override
  PhotoDetailScreenState createState() => PhotoDetailScreenState();
}

class PhotoDetailScreenState extends ConsumerState<PhotoDetailScreen> {
  final _scrollController = ScrollController();
  late final _photoDescriptionKey = GlobalKey();
  final _commentListService = CommentListService();
  final _commentFieldController = TextEditingController();
  final _focusNode = FocusNode();

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _pageIndex = 0;
  bool _loadMoreEndReached = false;
  bool _isProcessing = false;
  bool _isLoadingMore = false;
  final Map<int, GlobalKey> _activeCommentKeys = {};
  int? _activeCommentId;

  double? _loadMoreThreshold;

  final double _sectionSpacing = 20.0;
  late bool _isOwnPhoto;
  late bool _isArchived;

  late final int _profileId;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;
    _isOwnPhoto = widget.photo.authorId == _profileId;
    _isArchived = widget.originScreenName == 'archived_photos_screen';

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _commentFieldController.dispose();
    _focusNode.dispose();
    _scrollController.dispose();
    _activeCommentKeys.clear();
    super.dispose();
  }

  void _onScroll() {
    if (_canLoadMore()) _triggerLoadMore();
  }

  double _getLoadMoreThreshold() {
    if (_loadMoreThreshold == null) {
      final screenHeight = MediaQuery.of(context).size.height;
      final dynamicThreshold = screenHeight * LoadMoreConfig.tresholdByScreen;

      // Add bounds by min and max.
      _loadMoreThreshold = math.max(
        math.min(dynamicThreshold, LoadMoreConfig.maxTreshold),
        LoadMoreConfig.minTreshold,
      );
    }
    return _loadMoreThreshold!;
  }

  bool _canLoadMore() {
    return _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent -
                _getLoadMoreThreshold() &&
        !_isLoadingMore &&
        !_loadMoreEndReached;
  }

  void _triggerLoadMore() async {
    if (_isLoadingMore || _loadMoreEndReached) return;

    setState(() {
      _isLoadingMore = true;
    });

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    bool hasDescription = widget.photo.description.isNotEmpty;

    final List<CommentData> commentList = ref.watch(
      commentListProvider(widget.photo.id),
    );

    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              edgeOffset: LayoutConfig.bottomNavBarHeight,
              onRefresh: _handleRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  PmSliverAppBar(
                    scrollController: _scrollController,
                    titleText: widget.isPhotoDetail
                        ? "Photo details"
                        : "Comments",
                    useLogo: false,
                    automaticallyImplyLeading: true,
                    actions: [
                      StatefulBuilder(
                        builder: (ctx, setState) {
                          return ProfileMenuIndicator(
                            onTap: () {
                              if (_isOwnPhoto) {
                                _showOwnPhotoBottomSheet(
                                  archiveAssignmentAction: _isArchived
                                      ? AssignmentActionType.unassign
                                      : AssignmentActionType.assign,
                                  onArchivedStatusChanged: () {
                                    setState(() {
                                      _isArchived = !_isArchived;
                                    });
                                  },
                                );
                                return;
                              }

                              _showOtherPhotoBottomSheet();
                            },
                          );
                        },
                      ),
                    ],
                  ),
                  if (widget.isPhotoDetail)
                    SliverToBoxAdapter(
                      child: Align(
                        alignment: Alignment.topCenter,
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 768.0),
                          child: PhotoDetailFrame(
                            photo: widget.photo,
                            isProcessing: _isProcessing,
                          ),
                        ),
                      ),
                    ),
                  if (hasDescription)
                    SliverToBoxAdapter(
                      child: PhotoDetailDescription(
                        key: _photoDescriptionKey,
                        photo: widget.photo,
                        padding: EdgeInsets.only(
                          right: ScreenStyleConfig.horizontalPadding,
                          left: ScreenStyleConfig.horizontalPadding,
                          top: _sectionSpacing,
                        ),
                        contentToDividerGap: _sectionSpacing,
                      ),
                    ),
                  if (widget.isPhotoDetail)
                    SliverToBoxAdapter(
                      child: MaxWidth(
                        maxWidth: 768.0,
                        child: PhotoAuthor(
                          authorId: widget.photo.authorId,
                          authorNicename: widget.photo.authorNicename,
                          authorDisplayName: widget.photo.authorDisplayName,
                          authorProfileUrl: widget.photo.authorProfileUrl,
                          authorAvatarUrl: widget.photo.authorAvatarUrl,
                          authorMembershipType:
                              widget.photo.authorMembershipType,
                          padding: EdgeInsets.only(
                            right: ScreenStyleConfig.horizontalPadding,
                            left: ScreenStyleConfig.horizontalPadding,
                            top: _sectionSpacing,
                          ),
                        ),
                      ),
                    ),
                  if (widget.isPhotoDetail)
                    SliverToBoxAdapter(
                      child: Container(
                        constraints: const BoxConstraints(maxWidth: 768.0),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return PhotoMetadata(
                              photo: widget.photo,
                              padding: EdgeInsets.only(
                                left: ScreenStyleConfig.horizontalPadding,
                                right: ScreenStyleConfig.horizontalPadding,
                                top: _sectionSpacing,
                              ),
                              contentToDividerGap: _sectionSpacing,
                            );
                          },
                        ),
                      ),
                    ),
                  if (widget.isPhotoDetail &&
                      widget.photo.categories.isNotEmpty)
                    SliverToBoxAdapter(
                      child: PhotoCategories(
                        categoryIds: widget.photo.categories,
                        padding: EdgeInsets.only(
                          top: _sectionSpacing,
                          left: ScreenStyleConfig.horizontalPadding,
                          right: ScreenStyleConfig.horizontalPadding,
                        ),
                        contentToDividerGap: _sectionSpacing,
                      ),
                    ),
                  if (widget.isPhotoDetail)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.only(
                          top: _sectionSpacing,
                          left: ScreenStyleConfig.horizontalPadding,
                          right: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: const Text(
                          "Comments",
                          style: TextStyle(
                            fontSize: 15.0,
                            // fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  SliverToBoxAdapter(child: SizedBox(height: _sectionSpacing)),
                  SliverList(
                    delegate: SliverChildBuilderDelegate((
                      BuildContext context,
                      int index,
                    ) {
                      double marginTop = index == 0 ? 0 : 12.0;
                      final bool isOwnComment =
                          commentList[index].authorId == _profileId;

                      return Container(
                        margin: EdgeInsets.only(top: marginTop),
                        child: CommentListItem(
                          commentFieldController: _commentFieldController,
                          commentFieldFocusNode: _focusNode,
                          comment: commentList[index],
                          isOwnComment: isOwnComment,
                          onEditCommentTap: isOwnComment
                              ? () {
                                  final int commentId = commentList[index].id;

                                  setState(() {
                                    _activeCommentId = commentId;
                                    _activeCommentKeys[commentId] = GlobalKey();
                                  });

                                  handleEditCommentTap(
                                    ref: ref,
                                    comment: commentList[index],
                                    commentFieldController:
                                        _commentFieldController,
                                    commentFieldFocusNode: _focusNode,
                                  );
                                }
                              : null,
                          onDeleteCommentTap: isOwnComment
                              ? () async {
                                  final int commentId = commentList[index].id;
                                  final GlobalKey? targetKey =
                                      _activeCommentKeys[commentId];

                                  if (targetKey == null) {
                                    return;
                                  }

                                  DeleteCommentUtil(
                                    context: context,
                                    ref: ref,
                                    photoId: widget.photo.id,
                                    commentToDelete: commentList[index],
                                    targetKey: targetKey,
                                  ).handleDeleteEvent();
                                }
                              : null,
                        ),
                      );
                    }, childCount: commentList.length),
                  ),
                  if (_isLoadingMore && !_loadMoreEndReached)
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: CircularProgressIndicator(
                          color: context.colors.baseColorAlt,
                        ),
                      ),
                    ),
                  if (_loadMoreEndReached && commentList.isEmpty)
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          'No comments found',
                          style: TextStyle(
                            color: context.colors.primarySwatch[300],
                          ),
                        ),
                      ),
                    ),
                  if (commentList.isEmpty)
                    const SliverToBoxAdapter(child: SizedBox(height: 30.0)),
                ],
              ),
            ),
          ),
          CommentForm(
            photoId: widget.photo.id,
            fieldController: _commentFieldController,
            focusNode: _focusNode,
            photoDescriptionKey: _photoDescriptionKey,
            activeCommentKey: _activeCommentKeys[_activeCommentId],
          ),
        ],
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // Reset loading state
    _isLoadingMore = false;

    ref.read(commentActivityProvider.notifier).reset();

    _pageIndex = 0;
    _loadMoreEndReached = false;

    CommentListResponse response = await _commentListService.fetch(
      isRefreshAction: true,
      postId: widget.photo.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    _handleCommentListResponse(response, true, false);
  }

  Future<void> _handleLoadMore() async {
    CommentListResponse response = await _commentListService.fetch(
      postId: widget.photo.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    final isFirstLoad = _pageIndex == 0;

    _handleCommentListResponse(response, false, isFirstLoad);
  }

  void _handleCommentListResponse(
    CommentListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    if (response.data.isEmpty) {
      // Update the related photo's totalComments
      ref
          .read(photoStoreProvider.notifier)
          .setTotalComments(
            widget.photo.id,
            ref.read(commentListLengthProvider(widget.photo.id)),
          );

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref.read(commentStoreProvider.notifier).updateItems(response.data);

    _pageIndex++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(commentListManagerProvider.notifier)
          .replaceAll(widget.photo.id, commentListToIdList(response.data));
    } else {
      if (isFirstLoad) {
        ref
            .read(commentListManagerProvider.notifier)
            .replaceAll(widget.photo.id, commentListToIdList(response.data));
      } else {
        ref
            .read(commentListManagerProvider.notifier)
            .addAll(widget.photo.id, commentListToIdList(response.data));
      }
    }

    if (response.data.length < _loadMorePerPage) {
      // Update related photo's totalComments
      ref
          .read(photoStoreProvider.notifier)
          .setTotalComments(
            widget.photo.id,
            ref.read(commentListLengthProvider(widget.photo.id)),
          );

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }
    }
  }

  void _showOtherPhotoBottomSheet() {
    showModalBottomSheet(
      context: context,
      // isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return OtherPhotoBottomSheet(
          photo: widget.photo,
          isOwnPhoto: _isOwnPhoto,
          screenName: 'photo_detail_screen',
          onDeletePhotoByMod: (PhotoData photo) {
            _handleDeletePhoto(isModeration: true);
          },
          onFeaturedAssignment: _handleFeaturedAssignment,
          onPotdAssignment: _handlePotdAssignment,
        );
      },
    );
  }

  void _showOwnPhotoBottomSheet({
    AssignmentActionType? archiveAssignmentAction,
    VoidCallback? onArchivedStatusChanged,
  }) {
    showModalBottomSheet(
      context: context,
      // isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return OwnPhotoBottomSheet(
          photo: widget.photo,
          screenName: widget.originScreenName,
          archiveAssignmentType: archiveAssignmentAction,
          showExtraMenu: false,
          onPhotoArchiveAssignment:
              (AssignmentActionType actionType, PhotoData photo) {
                _handlePhotoArchiveAssignment(
                  photo: photo,
                  actionType: actionType,
                  onArchivedStatusChanged: onArchivedStatusChanged,
                );
              },
          onDeletePhoto: (PhotoData photo) {
            _handleDeletePhoto(isModeration: false);
          },
          onFeaturedAssignment: _handleFeaturedAssignment,
          onPotdAssignment: _handlePotdAssignment,
        );
      },
    );
  }

  void _startProcessing() {
    if (!mounted) return;

    setState(() {
      _isProcessing = true;
    });
  }

  void _stopProcessing({VoidCallback? callback}) {
    if (!mounted) return;

    setState(() {
      _isProcessing = false;
      callback?.call();
    });
  }

  void _handlePhotoArchiveAssignment({
    required PhotoData photo,
    required AssignmentActionType actionType,
    VoidCallback? onArchivedStatusChanged,
  }) async {
    ArchiveAssignmentUtil(
      ref: ref,
      photo: widget.photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
      onSuccess: (String msg) {
        _showSuccessSnackBar(msg);
        onArchivedStatusChanged?.call();
      },
      onSessionEndedError: _showSessionEndedDialog,
      onError: _showErrorSnackBar,
    ).handleAssignment(actionType: actionType);
  }

  void _handleDeletePhoto({bool isModeration = false}) async {
    PhotoDeletionUtil(
      context: context,
      ref: ref,
      photo: widget.photo,
      isOwnPhoto: _isOwnPhoto,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleDeletion(isPhotoReportModeration: isModeration);
  }

  void _handleFeaturedAssignment({
    required AssignmentActionType action,
    bool notifyAuthor = false,
  }) async {
    FeaturedAssignmentUtil(
      context: context,
      ref: ref,
      photo: widget.photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleAssignment(action: action, notifyAuthor: notifyAuthor);
  }

  void _handlePotdAssignment({required AssignmentActionType action}) async {
    PotdAssignmentUtil(
      context: context,
      ref: ref,
      photo: widget.photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleAssignment(action: action);
  }

  void _showSuccessSnackBar(String msg) {
    showAppSnackBar(
      context: context,
      message: msg,
      type: AppSnackBarType.success,
    );
  }

  void _showSessionEndedDialog() {
    showSessionEndedDialog(context, ref);
  }

  void _showErrorSnackBar(String msg) {
    showAppSnackBar(context: context, message: msg);
  }
}
